{"name": "scraping-tool", "version": "1.0.0", "description": "A comprehensive web scraping tool with React frontend and Node.js backend", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "format": "turbo run format", "clean": "turbo run clean", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:down": "docker-compose down"}, "devDependencies": {"turbo": "^1.10.0", "typescript": "^5.0.0", "@types/node": "^20.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "packageManager": "npm@9.0.0"}
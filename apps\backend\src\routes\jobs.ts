import { Router } from 'express'

const router = Router()

// Get all jobs
router.get('/', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 1,
        name: 'Tesco Electronics Scraping',
        status: 'completed',
        vendor: 'Tesco',
        category: 'Electronics',
        progress: 100,
        createdAt: '2024-01-15T10:30:00Z',
        completedAt: '2024-01-15T11:45:00Z',
      },
      {
        id: 2,
        name: 'Waitrose Groceries Scraping',
        status: 'running',
        vendor: 'Waitrose',
        category: 'Groceries',
        progress: 65,
        createdAt: '2024-01-15T12:00:00Z',
      },
    ],
  })
})

// Get job by ID
router.get('/:id', (req, res) => {
  const { id } = req.params
  res.json({
    success: true,
    data: {
      id: parseInt(id),
      name: 'Tesco Electronics Scraping',
      status: 'completed',
      vendor: 'Tesco',
      category: 'Electronics',
      progress: 100,
      createdAt: '2024-01-15T10:30:00Z',
      completedAt: '2024-01-15T11:45:00Z',
    },
  })
})

// Create new job
router.post('/', (req, res) => {
  const { name, vendor, category } = req.body
  res.status(201).json({
    success: true,
    data: {
      id: 3,
      name,
      vendor,
      category,
      status: 'pending',
      progress: 0,
      createdAt: new Date().toISOString(),
    },
  })
})

// Update job
router.put('/:id', (req, res) => {
  const { id } = req.params
  const { status, progress } = req.body
  res.json({
    success: true,
    data: {
      id: parseInt(id),
      status,
      progress,
      updatedAt: new Date().toISOString(),
    },
  })
})

// Delete job
router.delete('/:id', (req, res) => {
  const { id } = req.params
  res.json({
    success: true,
    message: `Job ${id} deleted successfully`,
  })
})

export default router 
<context>
# Overview  
A specialized web scraping application designed to extract product data from major UK retail websites using Stagehand browser automation. This application will provide comprehensive product intelligence by gathering real-time pricing, availability, and product information from leading UK retailers across multiple categories including grocery, retail, toys, family, and warehouse stores. The tool will enable businesses, researchers, and analysts to monitor market trends, track competitor pricing, and gather comprehensive product catalogs from the UK's most prominent retail chains.

# Core Features  
## Stagehand Browser Automation Engine
- Advanced browser automation using Stagehand for reliable data extraction
- Anti-bot detection evasion with rotating user agents and session management
- Rate limiting and respectful crawling to maintain access to target sites
- Concurrent scraping capabilities across multiple retail sites
- Session persistence and cookie handling for authenticated scraping

## Multi-Vendor Product Data Extraction
- **Grocery Retailers**: Waitrose, Tesco, ASDA
- **Fashion Retailers**: Selfridges, Next, Primark  
- **Toy Retailers**: Hamleys, The Toy Shop
- **Family Retailers**: Mamas & Papas
- **Warehouse Retailers**: Costco
- Structured data extraction: product names, prices, availability, images, descriptions

## Data Processing Pipeline
- Real-time data validation and quality checks
- Product deduplication across vendors and categories
- Price normalization and currency handling
- Image URL extraction and metadata processing
- Structured output in multiple formats (CSV, JSON, Excel)

## User Interface
- Comprehensive dashboard for managing scraping jobs across all vendors
- Real-time progress tracking with visual indicators
- Product catalog view organized by vendor/category
- Advanced search and filter functionality
- Data export capabilities with scheduling options

## Storage and Management
- PostgreSQL database for structured product data storage
- Vendor attribution and timestamp tracking
- Historical price tracking and trend analysis
- Backup and data retention policies
- API endpoints for external data access

# User Experience  
## Primary User Personas
- **Business Analysts**: Monitor competitor pricing and market trends
- **Retail Researchers**: Track product availability and pricing across UK retailers
- **Data Scientists**: Gather large datasets for market analysis and ML models
- **E-commerce Managers**: Monitor product positioning and pricing strategies
- **Market Researchers**: Analyze retail trends and consumer behavior

## Key User Flows
1. **Job Creation**: User selects target vendors, categories, and scraping parameters
2. **Real-time Monitoring**: Dashboard shows live progress across all active scraping sessions
3. **Data Exploration**: Browse and search through collected product catalogs
4. **Export & Analysis**: Download filtered data sets for further analysis
5. **Scheduling**: Set up automated scraping jobs for continuous market monitoring

## UI/UX Considerations
- Intuitive vendor selection with category grouping
- Real-time progress bars and status indicators
- Advanced filtering by price range, availability, vendor, category
- Responsive design for desktop and tablet access
- Dark/light theme options for extended monitoring sessions
- Export options with custom date ranges and filters
</context>
<PRD>
# Technical Architecture  
## System Components
- **Frontend**: React-based web application with TypeScript and Tailwind CSS
- **Backend**: Node.js/Express API server with TypeScript
- **Scraping Engine**: Stagehand browser automation for reliable data extraction
- **Database**: PostgreSQL for product data storage and job management
- **Queue System**: Redis for job queuing and concurrent scraping management
- **Storage**: File system for data exports and cloud storage integration

## Data Models
- **Products**: Product information with vendor attribution and timestamps
- **Vendors**: Retailer information and scraping configurations
- **Jobs**: Scraping job definitions and execution status
- **Schedules**: Automated scraping job configurations
- **Users**: User accounts and permission management
- **Categories**: Product categorization and filtering

## APIs and Integrations
- RESTful API for job management and data access
- WebSocket connections for real-time progress updates
- Export APIs for external system integration
- Vendor-specific scraping adapters and configurations
- Third-party integrations (Google Sheets, Slack notifications)

## Infrastructure Requirements
- Containerized deployment with Docker and docker-compose
- Load balancing for high availability
- Monitoring and logging with Prometheus/Grafana
- Backup and disaster recovery systems
- Rate limiting and anti-bot detection management

# Development Roadmap  
## Phase 1: MVP Foundation
- Stagehand browser automation setup and configuration
- Basic scraping engine for single vendor (Tesco)
- Simple command-line interface for testing
- Core data extraction functionality
- Basic error handling and logging
- Single-page web interface for job management
- SQLite database for local development

## Phase 2: Multi-Vendor Support
- Vendor-specific scraping adapters for all target retailers
- Advanced selector configuration for each vendor
- Data processing pipeline with validation and deduplication
- Export functionality (CSV, JSON, Excel)
- Job scheduling and automation
- User authentication and basic permissions
- PostgreSQL database integration

## Phase 3: Production Features
- Anti-bot detection and rate limiting for all vendors
- Session management and cookie handling
- Real-time monitoring dashboard with progress tracking
- Advanced error handling and retry mechanisms
- Concurrent scraping across multiple vendors
- API endpoints for external integrations
- Docker containerization and deployment

## Phase 4: Advanced Capabilities
- Advanced search and filtering capabilities
- Historical price tracking and trend analysis
- Data validation and quality assurance
- Advanced scheduling with cron expressions
- Webhook notifications and alerts
- Performance optimization and horizontal scaling
- Vendor-specific data enrichment and categorization

# Logical Dependency Chain
## Foundation First (Phase 1)
1. **Stagehand Setup**: Configure Stagehand browser automation environment
2. **Core Scraping Engine**: Build basic scraping functionality with Stagehand
3. **Data Models**: Create database schema for products, vendors, and jobs
4. **Basic API**: REST endpoints for job management and data access
5. **Simple UI**: Single-page interface for job creation and monitoring
6. **CLI Interface**: Command-line tool for testing scraping functionality

## Multi-Vendor Support (Phase 2)
7. **Vendor Adapters**: Create scraping adapters for each target retailer
8. **Data Processing**: Implement validation, deduplication, and normalization
9. **Export System**: Multiple output formats for product data
10. **Scheduling**: Basic job scheduling capabilities
11. **Authentication**: User login and basic security
12. **Database Migration**: Move from SQLite to PostgreSQL

## Production Ready (Phase 3)
13. **Anti-bot Detection**: Implement evasion techniques for all vendors
14. **Rate Limiting**: Respectful crawling with vendor-specific delays
15. **Real-time Updates**: WebSocket connections for live monitoring
16. **Concurrent Scraping**: Multi-vendor simultaneous processing
17. **Error Handling**: Robust error recovery and logging
18. **API Integration**: External system connections

## Advanced Features (Phase 4)
19. **Advanced Search**: Complex filtering and search capabilities
20. **Historical Tracking**: Price history and trend analysis
21. **Data Validation**: Quality checks and data integrity
22. **Advanced Scheduling**: Complex timing configurations
23. **Notifications**: Webhook and email alerts
24. **Performance**: Optimization and horizontal scaling

# Risks and Mitigations  
## Technical Challenges
- **Risk**: Target websites implementing aggressive anti-bot measures
- **Mitigation**: Advanced Stagehand configurations, rotating user agents, proxy support, and respectful crawling patterns

- **Risk**: Vendor website structure changes breaking scrapers
- **Mitigation**: Flexible selector system with fallback options, monitoring, and rapid adapter updates

- **Risk**: Large-scale data processing performance with multiple vendors
- **Mitigation**: Streaming data processing, database optimization, and concurrent processing management

## MVP Scope Management
- **Risk**: Feature creep delaying initial release
- **Mitigation**: Focus on core Stagehand scraping functionality first, add vendors incrementally

- **Risk**: Complex UI slowing development
- **Mitigation**: Start with simple CLI and basic web interface, enhance iteratively

## Resource Constraints
- **Risk**: Memory usage with large product datasets
- **Mitigation**: Implement streaming and pagination for data processing

- **Risk**: Storage costs for extensive product catalogs
- **Mitigation**: Data compression, retention policies, and cloud storage options

- **Risk**: Rate limiting affecting scraping reliability
- **Mitigation**: Intelligent scheduling, proxy rotation, and vendor-specific delays

# Appendix  
## Research Findings
- Stagehand provides superior browser automation compared to Puppeteer for complex retail sites
- PostgreSQL offers better performance for structured product data than NoSQL alternatives
- Redis is optimal for job queuing and concurrent session management
- React with TypeScript provides better developer experience and type safety
- UK retail sites have varying anti-bot measures requiring vendor-specific strategies

## Technical Specifications
- **Frontend**: React 18+, TypeScript, Tailwind CSS
- **Backend**: Node.js 18+, Express, TypeScript
- **Database**: PostgreSQL 15+
- **Scraping**: Stagehand browser automation
- **Queue**: Redis 7+
- **Container**: Docker with docker-compose
- **Monitoring**: Prometheus, Grafana, Winston logging

## Target Vendor Analysis
- **Grocery**: Tesco, Waitrose, ASDA - Complex category structures, frequent updates
- **Fashion**: Selfridges, Next, Primark - Large product catalogs, seasonal changes
- **Toys**: Hamleys, The Toy Shop - Seasonal inventory, promotional pricing
- **Family**: Mamas & Papas - Specialized categories, limited product ranges
- **Warehouse**: Costco - Bulk pricing, membership requirements, large inventories 
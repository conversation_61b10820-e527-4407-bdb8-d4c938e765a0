# Scraping Tool

A comprehensive web scraping tool with React/TypeScript frontend and Node.js/Express backend for extracting product data from UK retail websites.

## 🚀 Features

- **Multi-vendor scraping**: Support for Tesco, Waitrose, ASDA, Selfridges, Next, Primark, Hamleys, The Toy Shop, Mamas & Papas, and Costco
- **Real-time monitoring**: WebSocket-based progress updates
- **Advanced anti-bot detection**: Sophisticated evasion techniques
- **Concurrent processing**: Redis-based job queuing system
- **Data export**: Multiple format support (CSV, JSON, Excel)
- **Historical tracking**: Price change monitoring and trend analysis

## 🏗️ Architecture

This project uses a monorepo structure with:

- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript
- **Database**: PostgreSQL
- **Queue System**: Redis
- **Browser Automation**: Stagehand
- **Build System**: Turborepo

## 📁 Project Structure

```
scraping-tool/
├── apps/
│   ├── frontend/          # React application
│   └── backend/           # Express API server
├── packages/
│   └── shared/            # Shared utilities and types
├── .taskmaster/           # Task management
└── docker-compose.yml     # Container orchestration
```

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+
- npm 8+
- Docker & Docker Compose
- PostgreSQL
- Redis

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd scraping-tool
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp env.example .env
   # Edit .env with your configuration
   ```

4. **Start development servers**
   ```bash
   npm run dev
   ```

### Available Scripts

- `npm run dev` - Start all development servers
- `npm run build` - Build all applications
- `npm run test` - Run tests across all packages
- `npm run lint` - Lint all code
- `npm run format` - Format all code
- `npm run clean` - Clean all build artifacts
- `npm run docker:build` - Build Docker containers
- `npm run docker:up` - Start Docker services
- `npm run docker:down` - Stop Docker services

## 🐳 Docker

```bash
# Build and start all services
npm run docker:build
npm run docker:up

# Stop services
npm run docker:down
```

## 📊 API Documentation

The API documentation is available at `/api/docs` when the backend server is running.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions, please open an issue in the GitHub repository. 
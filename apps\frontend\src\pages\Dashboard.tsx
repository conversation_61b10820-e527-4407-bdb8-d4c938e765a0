import { BarChart3, TrendingUp, Clock, CheckCircle } from 'lucide-react'

const Dashboard = () => {
  const stats = [
    {
      name: 'Total Jobs',
      value: '24',
      change: '+12%',
      changeType: 'positive',
      icon: BarChart3,
    },
    {
      name: 'Active Jobs',
      value: '8',
      change: '+3%',
      changeType: 'positive',
      icon: Clock,
    },
    {
      name: 'Completed Jobs',
      value: '16',
      change: '+8%',
      changeType: 'positive',
      icon: CheckCircle,
    },
    {
      name: 'Success Rate',
      value: '94%',
      change: '+2%',
      changeType: 'positive',
      icon: TrendingUp,
    },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Monitor your scraping jobs and system status</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <stat.icon className="h-8 w-8 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                <p className="text-sm text-green-600">{stat.change}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-600">Tesco scraping job completed successfully</span>
            <span className="text-xs text-gray-400">2 minutes ago</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-sm text-gray-600">Waitrose scraping job started</span>
            <span className="text-xs text-gray-400">5 minutes ago</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span className="text-sm text-gray-600">ASDA scraping job in progress</span>
            <span className="text-xs text-gray-400">10 minutes ago</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard 
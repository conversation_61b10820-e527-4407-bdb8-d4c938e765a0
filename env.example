# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/scraping_tool
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=scraping_tool
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Server Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# API Keys (if needed for external services)
# GOOGLE_SHEETS_API_KEY=
# SLACK_WEBHOOK_URL=

# Browserbase Configuration (if using Browserbase)
# BROWSERBASE_API_KEY=

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100 
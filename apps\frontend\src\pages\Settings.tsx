import { Save } from 'lucide-react'

const Settings = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Configure your scraping tool preferences</p>
      </div>

      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">General Settings</h2>
        <div className="space-y-4">
          <div>
            <label htmlFor="api-url" className="block text-sm font-medium text-gray-700">
              API URL
            </label>
            <input
              type="text"
              id="api-url"
              className="input mt-1"
              defaultValue="http://localhost:3001"
            />
          </div>
          <div>
            <label htmlFor="websocket-url" className="block text-sm font-medium text-gray-700">
              WebSocket URL
            </label>
            <input
              type="text"
              id="websocket-url"
              className="input mt-1"
              defaultValue="ws://localhost:3001"
            />
          </div>
          <div>
            <label htmlFor="refresh-interval" className="block text-sm font-medium text-gray-700">
              Refresh Interval (seconds)
            </label>
            <input
              type="number"
              id="refresh-interval"
              className="input mt-1"
              defaultValue="30"
              min="5"
              max="300"
            />
          </div>
        </div>
      </div>

      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Notification Settings</h2>
        <div className="space-y-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="email-notifications"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              defaultChecked
            />
            <label htmlFor="email-notifications" className="ml-2 block text-sm text-gray-900">
              Email notifications
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="browser-notifications"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              defaultChecked
            />
            <label htmlFor="browser-notifications" className="ml-2 block text-sm text-gray-900">
              Browser notifications
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="sound-notifications"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor="sound-notifications" className="ml-2 block text-sm text-gray-900">
              Sound notifications
            </label>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <button className="btn btn-primary flex items-center">
          <Save className="h-4 w-4 mr-2" />
          Save Settings
        </button>
      </div>
    </div>
  )
}

export default Settings 
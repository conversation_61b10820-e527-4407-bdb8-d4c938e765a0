{"name": "@scraping-tool/frontend", "version": "1.0.0", "description": "React frontend for the scraping tool", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "clean": "rm -rf dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "axios": "^1.6.0", "socket.io-client": "^4.7.0", "lucide-react": "^0.263.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "postcss": "^8.4.0", "prettier": "^3.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^4.4.0", "vitest": "^0.34.0", "@vitest/ui": "^0.34.0"}}
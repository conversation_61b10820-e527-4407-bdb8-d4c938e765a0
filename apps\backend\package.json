{"name": "@scraping-tool/backend", "version": "1.0.0", "description": "Express API server for the scraping tool", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "clean": "rm -rf dist"}, "dependencies": {"express": "^4.18.0", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.0", "express-rate-limit": "^7.1.0", "express-validator": "^7.0.0", "socket.io": "^4.7.0", "redis": "^4.6.0", "pg": "^8.11.0", "winston": "^3.10.0", "compression": "^1.7.4", "express-async-errors": "^3.1.1"}, "devDependencies": {"@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@types/morgan": "^1.9.0", "@types/compression": "^1.7.0", "@types/pg": "^8.10.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0", "tsx": "^4.0.0", "vitest": "^0.34.0", "@vitest/ui": "^0.34.0", "supertest": "^6.3.0", "@types/supertest": "^2.0.0"}}